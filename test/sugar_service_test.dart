import 'package:flutter_test/flutter_test.dart';
import 'package:health_diary/health_service/sugar_service.dart';
import 'package:health_diary/types/health_types.dart';

void main() {
  group('SugarService', () {
    test('calculateFromTime should correctly identify breakfast time', () {
      // 早餐时间 6:30
      final dateTime = DateTime(2023, 1, 1, 6, 30);
      final result = SugarService.calculateFromTime(dateTime);
      
      expect(result.mealTime, equals(MealTimeEnum.breakfast));
      expect(result.bloodSugarType, equals(BloodSugarTypeEnum.fasting));
    });

    test('calculateFromTime should correctly identify lunch time', () {
      // 午餐时间 12:30
      final dateTime = DateTime(2023, 1, 1, 12, 30);
      final result = SugarService.calculateFromTime(dateTime);
      
      expect(result.mealTime, equals(MealTimeEnum.lunch));
      expect(result.bloodSugarType, equals(BloodSugarTypeEnum.afterMeal));
    });

    test('calculateFromTime should correctly identify afternoon tea time', () {
      // 下午茶时间 15:30
      final dateTime = DateTime(2023, 1, 1, 15, 30);
      final result = SugarService.calculateFromTime(dateTime);
      
      expect(result.mealTime, equals(MealTimeEnum.afternoonTea));
      expect(result.bloodSugarType, equals(BloodSugarTypeEnum.afterMeal));
    });

    test('calculateFromTime should correctly identify pre-afternoon tea time', () {
      // 下午茶前 14:30
      final dateTime = DateTime(2023, 1, 1, 14, 30);
      final result = SugarService.calculateFromTime(dateTime);
      
      expect(result.mealTime, equals(MealTimeEnum.afternoonTea));
      expect(result.bloodSugarType, equals(BloodSugarTypeEnum.beforeMeal));
    });

    test('calculateFromTime should correctly identify dinner time', () {
      // 晚餐时间 19:30
      final dateTime = DateTime(2023, 1, 1, 19, 30);
      final result = SugarService.calculateFromTime(dateTime);
      
      expect(result.mealTime, equals(MealTimeEnum.dinner));
      expect(result.bloodSugarType, equals(BloodSugarTypeEnum.afterMeal));
    });

    test('calculateFromTime should correctly identify bedtime', () {
      // 睡前时间 23:30
      final dateTime = DateTime(2023, 1, 1, 23, 30);
      final result = SugarService.calculateFromTime(dateTime);
      
      expect(result.mealTime, equals(MealTimeEnum.bedtime));
      expect(result.bloodSugarType, equals(BloodSugarTypeEnum.afterMeal));
    });
  });
}
