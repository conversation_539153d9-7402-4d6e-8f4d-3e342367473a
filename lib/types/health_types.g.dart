// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_types.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BloodPressureData _$BloodPressureDataFromJson(Map<String, dynamic> json) =>
    BloodPressureData(
      systolic: (json['systolic'] as num).toInt(),
      diastolic: (json['diastolic'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      pulse: (json['pulse'] as num?)?.toInt(),
      note: json['note'] as String?,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$BloodPressureDataToJson(BloodPressureData instance) =>
    <String, dynamic>{
      'systolic': instance.systolic,
      'diastolic': instance.diastolic,
      'createdAt': instance.createdAt.toIso8601String(),
      'pulse': instance.pulse,
      'note': instance.note,
      'runtimeType': instance.$type,
    };

BloodSugarData _$BloodSugarDataFromJson(Map<String, dynamic> json) =>
    BloodSugarData(
      value: (json['value'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      bloodSugarType: $enumDecodeNullable(
        _$BloodSugarTypeEnumEnumMap,
        json['bloodSugarType'],
      ),
      mealTime: $enumDecodeNullable(_$MealTimeEnumEnumMap, json['mealTime']),
      note: json['note'] as String?,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$BloodSugarDataToJson(BloodSugarData instance) =>
    <String, dynamic>{
      'value': instance.value,
      'createdAt': instance.createdAt.toIso8601String(),
      'bloodSugarType': _$BloodSugarTypeEnumEnumMap[instance.bloodSugarType],
      'mealTime': _$MealTimeEnumEnumMap[instance.mealTime],
      'note': instance.note,
      'runtimeType': instance.$type,
    };

const _$BloodSugarTypeEnumEnumMap = {
  BloodSugarTypeEnum.fasting: 'fasting',
  BloodSugarTypeEnum.beforeMeal: 'beforeMeal',
  BloodSugarTypeEnum.afterMeal: 'afterMeal',
};

const _$MealTimeEnumEnumMap = {
  MealTimeEnum.breakfast: 'breakfast',
  MealTimeEnum.lunch: 'lunch',
  MealTimeEnum.afternoonTea: 'afternoonTea',
  MealTimeEnum.dinner: 'dinner',
  MealTimeEnum.bedtime: 'bedtime',
};
