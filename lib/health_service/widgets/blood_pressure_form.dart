import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:health_diary/health_service/controller/data_controller.dart';
import 'package:health_diary/types/health_types.dart';

class BloodPressureForm extends ConsumerStatefulWidget {
  final GlobalKey<FormState> formKey;

  const BloodPressureForm({super.key, required this.formKey});

  @override
  ConsumerState<BloodPressureForm> createState() => _BloodPressureFormState();
}

class _BloodPressureFormState extends ConsumerState<BloodPressureForm> {
  final _dateTimeController = TextEditingController();
  final _systolicController = TextEditingController();
  final _diastolicController = TextEditingController();
  final _pulseController = TextEditingController();
  final _noteController = TextEditingController();
  final _systolicFocusNode = FocusNode();
  final _diastolicFocusNode = FocusNode();
  final _pulseFocusNode = FocusNode();
  final _noteFocusNode = FocusNode();

  @override
  void dispose() {
    _dateTimeController.dispose();
    _systolicController.dispose();
    _diastolicController.dispose();
    _pulseController.dispose();
    _noteController.dispose();
    _systolicFocusNode.dispose();
    _diastolicFocusNode.dispose();
    _pulseFocusNode.dispose();
    _noteFocusNode.dispose();
    super.dispose();
  }

  KeyboardActionsConfig _buildConfig(BuildContext context) {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.ALL,
      keyboardBarColor: Colors.grey[200],
      nextFocus: true,
      actions: [
        KeyboardActionsItem(focusNode: _systolicFocusNode),
        KeyboardActionsItem(focusNode: _diastolicFocusNode),
        KeyboardActionsItem(focusNode: _pulseFocusNode),
        KeyboardActionsItem(focusNode: _noteFocusNode),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final data = ref.watch(
      dataControllerProvider.select(
        (m) => m[HealthRecordTypeEnum.bloodPressure],
      ),
    );

    // 初始化表单数据
    DateTime selectedDateTime = DateTime.now();
    if (data is BloodPressureData) {
      if (data.systolic != 0) {
        _systolicController.text = data.systolic.toString();
      }
      if (data.diastolic != 0) {
        _diastolicController.text = data.diastolic.toString();
      }
      _pulseController.text = data.pulse?.toString() ?? '';
      _noteController.text = data.note ?? '';
      selectedDateTime = data.createdAt;
    }

    _dateTimeController.text = DateFormat(
      'yyyy年MM月dd日 HH:mm',
    ).format(selectedDateTime);

    return KeyboardActions(
      config: _buildConfig(context),
      disableScroll: true,
      child: Form(
        key: widget.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 日期时间输入
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'time'.tr(),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _dateTimeController,
                  decoration: InputDecoration(
                    hintText: 'select_date_time'.tr(),
                    hintStyle: Theme.of(context).textTheme.labelMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                    suffixIcon: Icon(Icons.access_time),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                  ),
                  readOnly: true,
                  onTap: () => _selectDateTime(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // 收缩压（高压）
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'systolic_pressure'.tr(),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _systolicController,
                  focusNode: _systolicFocusNode,
                  decoration: InputDecoration(
                    hintText: 'enter_value'.tr(),
                    suffixText: 'mmhg'.tr(),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                    hintStyle: Theme.of(context).textTheme.labelMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.next,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (value) {
                    final number = int.tryParse(value);
                    if (number != null) {
                      ref
                          .read(dataControllerProvider.notifier)
                          .updateBloodPressure(systolic: number);
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'required_field'.tr();
                    }
                    final number = int.tryParse(value);
                    if (number == null || number < 55 || number > 300) {
                      return 'invalid_systolic'.tr();
                    }
                    return null;
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 舒张压（低压）
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'diastolic_pressure'.tr(),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _diastolicController,
                  focusNode: _diastolicFocusNode,
                  decoration: InputDecoration(
                    hintText: 'enter_value'.tr(),
                    hintStyle: Theme.of(context).textTheme.labelMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                    suffixText: 'mmhg'.tr(),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.next,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (value) {
                    final number = int.tryParse(value);
                    if (number != null) {
                      ref
                          .read(dataControllerProvider.notifier)
                          .updateBloodPressure(diastolic: number);
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'required_field'.tr();
                    }
                    final number = int.tryParse(value);
                    if (number == null || number < 30 || number > 200) {
                      return 'invalid_diastolic'.tr();
                    }
                    return null;
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 脉搏输入（可选）
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'pulse_optional'.tr(),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _pulseController,
                  focusNode: _pulseFocusNode,
                  decoration: InputDecoration(
                    hintText: 'enter_value'.tr(),
                    hintStyle: Theme.of(context).textTheme.labelMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                    suffixText: 'times_per_minute'.tr(),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.next,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (value) {
                    final number = value.isNotEmpty
                        ? int.tryParse(value)
                        : null;
                    ref
                        .read(dataControllerProvider.notifier)
                        .updateBloodPressure(pulse: number);
                  },
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final number = int.tryParse(value);
                      if (number == null || number < 30 || number > 200) {
                        return 'invalid_pulse'.tr();
                      }
                    }
                    return null;
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 备注输入
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'note_optional'.tr(),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _noteController,
                  focusNode: _noteFocusNode,
                  decoration: InputDecoration(
                    hintText: 'add_note_hint'.tr(),
                    hintStyle: Theme.of(context).textTheme.labelMedium
                        ?.copyWith(fontWeight: FontWeight.w500),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                  ),
                  textInputAction: TextInputAction.done,
                  onChanged: (value) {
                    final note = value.isNotEmpty ? value : null;
                    ref
                        .read(dataControllerProvider.notifier)
                        .updateBloodPressure(note: note);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateTime() async {
    final currentTime = DateTime.now();

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: currentTime,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (pickedDate != null && mounted) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(currentTime),
      );

      if (pickedTime != null) {
        final newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );
        ref.read(dataControllerProvider.notifier).updateBloodPressure(
          time: newDateTime
        );
      }
    }
  }
}
