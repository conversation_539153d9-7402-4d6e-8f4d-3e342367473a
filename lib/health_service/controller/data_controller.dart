import 'package:health_diary/health_service/service_manager.dart';
import 'package:health_diary/health_service/sugar_service.dart';
import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'data_controller.g.dart';

@riverpod
class DataController extends _$DataController {
  @override
  Map<HealthRecordTypeEnum, HealthRecordData?> build() {
    final sugarTimeInfo = SugarService.calculateFromCurrentTime();
    final now = DateTime.now();
    return <HealthRecordTypeEnum, HealthRecordData?>{
      HealthRecordTypeEnum.bloodSugar: HealthRecordData.bloodSugar(
        value: 0,
        bloodSugarType: sugarTimeInfo.bloodSugarType,
        mealTime: sugarTimeInfo.mealTime,
        createdAt: now,
      ),
      HealthRecordTypeEnum.bloodPressure: null,
    };
  }

  void updateBloodSugar({
    double? value,
    BloodSugarTypeEnum? bloodSugarType,
    MealTimeEnum? mealTime,
    String? note,
    DateTime? time,
  }) {
    final old = state[HealthRecordTypeEnum.bloodSugar] as BloodSugarData?;
    state = {
      ...state,
      HealthRecordTypeEnum.bloodSugar: BloodSugarData(
        value: value ?? old?.value ?? 0,
        bloodSugarType: bloodSugarType ?? old?.bloodSugarType,
        mealTime: mealTime ?? old?.mealTime,
        note: note ?? old?.note,
        createdAt: time ?? old?.createdAt ?? DateTime.now(),
      ),
    };
  }

  void updateBloodPressure({
    int? systolic,
    int? diastolic,
    int? pulse,
    String? note,
    DateTime? time,
  }) {
    final old = state[HealthRecordTypeEnum.bloodPressure] as BloodPressureData?;
    state = {
      ...state,
      HealthRecordTypeEnum.bloodPressure: BloodPressureData(
        systolic: systolic ?? old?.systolic ?? 0,
        diastolic: diastolic ?? old?.diastolic ?? 0,
        pulse: pulse ?? old?.pulse,
        note: note ?? old?.note,
        createdAt: time ?? old?.createdAt ?? DateTime.now(),
      ),
    };
  }

  bool parseScanData(List<int> data, HealthRecordTypeEnum recordType) {
    final service = HealthServiceManager.getService(recordType);
    final ret = service.parseScannerData(data);
    if (ret == null) {
      return false;
    }
    state = {...state, recordType: ret};

    return true;
  }

  HealthRecordData? getRecordData(HealthRecordTypeEnum recordType) {
    return state[recordType];
  }

  /// 设置记录数据（用于编辑模式）
  void setRecordData(HealthRecordTypeEnum recordType, HealthRecordData data) {
    // 更新 createdAt 时间为当前时间
    final updatedData = switch (data) {
      BloodPressureData(systolic: final systolic, diastolic: final diastolic, pulse: final pulse, note: final note) =>
        HealthRecordData.bloodPressure(
          systolic: systolic,
          diastolic: diastolic,
          pulse: pulse,
          note: note,
          createdAt: data.createdAt,
        ),
      BloodSugarData(value: final value, bloodSugarType: final bloodSugarType, mealTime: final mealTime, note: final note) =>
        HealthRecordData.bloodSugar(
          value: value,
          bloodSugarType: bloodSugarType,
          mealTime: mealTime,
          note: note,
          createdAt: data.createdAt,
        ),
    };
    state = {...state, recordType: updatedData};
  }
}
