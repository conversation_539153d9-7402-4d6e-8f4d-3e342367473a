import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:health_diary/health_service/controller/data_controller.dart';
import 'package:health_diary/providers/scan_service/scan_provider.dart';
import 'package:health_diary/repository/health_record_repository.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_types.dart';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'add_record_controller.g.dart';
part 'add_record_controller.freezed.dart';

enum AddRecordInputType { scan, manual }

@freezed
abstract class AddRecordState with _$AddRecordState {
  factory AddRecordState({
    required AddRecordInputType inputType,
    required HealthRecordTypeEnum recordType,
    @Default(false) bool hasScanError,
  }) = _AddRecordState;
}

@riverpod
class AddRecordController extends _$AddRecordController {
  @override
  AddRecordState build() {
    ref.watch(dataControllerProvider.notifier);
    return AddRecordState(
        inputType: AddRecordInputType.scan,
        recordType: HealthRecordTypeEnum.bloodPressure
    );
  }

  void clearScanError() {
    state = state.copyWith(hasScanError: false);
  }

  void setInputType(AddRecordInputType inputType) {
    state = state.copyWith(inputType: inputType);
  }

  void setRecordType(HealthRecordTypeEnum recordType) {
    state = state.copyWith(recordType: recordType);
  }

  void setInitialData(HealthRecord record) {
    final dataController = ref.read(dataControllerProvider.notifier);
    dataController.setRecordData(record.type, record.data);
  }

  /// 扫描数据，成功后跳转手动输入页面
  Future<void> scan({required Uint8List imageData}) async {
    // 清除之前的错误状态
    state = state.copyWith(hasScanError: false);

    final List<int> result = await ScanService.scan(imageData: imageData);
    debugPrint('Scan result: $result');

    if (result.isEmpty) {
      // 设置扫描错误状态
      state = state.copyWith(hasScanError: true);
      return;
    }

    final dataController = ref.read(dataControllerProvider.notifier);
    final parseResult = dataController.parseScanData(result, state.recordType);

    if (!parseResult) {
      // 设置扫描错误状态
      state = state.copyWith(hasScanError: true);
    }

    state = state.copyWith(inputType: AddRecordInputType.manual);

    return;
  }

  /// 保存当前记录到数据库
  Future<bool> saveCurrentRecord() async {
    try {
      final data = ref.read(dataControllerProvider.notifier).getRecordData(
          state.recordType
      );

      if (data == null) {
        debugPrint('No record data to save');
        return false;
      }

      // 使用 HealthRecordData 中的 createdAt 字段，如果为 null 则使用当前时间
      final createdAt = switch (data) {
        BloodPressureData(createdAt: final createdAt) => createdAt ?? DateTime.now(),
        BloodSugarData(createdAt: final createdAt) => createdAt ?? DateTime.now(),
      };

      final repository = ref.read(healthRecordRepositoryProvider);
      final id = await repository.addHealthRecord(
          type: state.recordType,
          data: data,
          createdAt: createdAt
      );

      debugPrint('Record saved with id: $id');
      return true;
    } catch (e) {
      debugPrint('Error saving record: $e');
      return false;
    }
  }

  /// 更新现有记录到数据库
  Future<bool> updateCurrentRecord(HealthRecord originalRecord) async {
    try {
      final data = ref.read(dataControllerProvider.notifier).getRecordData(
          state.recordType
      );

      if (data == null) {
        debugPrint('No record data to update');
        return false;
      }

      // 使用 HealthRecordData 中的 createdAt 字段，如果为 null 则使用原记录的时间
      final createdAt = switch (data) {
        BloodPressureData(createdAt: final createdAt) => createdAt ?? originalRecord.createdAt,
        BloodSugarData(createdAt: final createdAt) => createdAt ?? originalRecord.createdAt,
      };

      final repository = ref.read(healthRecordRepositoryProvider);

      // 创建更新后的记录
      final updatedRecord = HealthRecord(
        id: originalRecord.id,
        type: state.recordType,
        data: data,
        createdAt: createdAt,
      );

      final success = await repository.updateHealthRecord(updatedRecord);

      debugPrint('Record updated: $success');
      return success;
    } catch (e) {
      debugPrint('Error updating record: $e');
      return false;
    }
  }


}
