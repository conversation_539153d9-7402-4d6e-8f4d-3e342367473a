// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_record_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addRecordControllerHash() =>
    r'e4a55b94ff3210ed7474d215919b0991e1987a58';

/// See also [AddRecordController].
@ProviderFor(AddRecordController)
final addRecordControllerProvider =
    AutoDisposeNotifierProvider<AddRecordController, AddRecordState>.internal(
      AddRecordController.new,
      name: r'addRecordControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$addRecordControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AddRecordController = AutoDisposeNotifier<AddRecordState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
