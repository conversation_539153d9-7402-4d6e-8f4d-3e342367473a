import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:health_diary/health_service/health_service.dart';
import 'package:health_diary/pages/add_record/controller/add_record_controller.dart';

class InputForm extends ConsumerStatefulWidget {
  const InputForm({super.key, required this.service, required this.canScan, this.onSave});

  final HealthService service;
  final bool canScan;
  final VoidCallback? onSave;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _InputFormState();
}

class _InputFormState extends ConsumerState<InputForm> {
  final _formKey = GlobalKey<FormState>();


  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          widget.service.buildInputForm(formKey: _formKey),
          const SizedBox(height: 24),

          // 提交按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _onSave,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'save_record'.tr(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          if (widget.canScan) ...[
            // 返回扫描按钮
            SizedBox(
              width: double.infinity,
              child: TextButton.icon(
                onPressed: () => _backToScan(ref),
                icon: const Icon(Icons.search, size: 18),
                label: Text('back_to_scan'.tr()),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _onSave() {
    if (!(_formKey.currentState?.validate() ?? false)) {
      return;
    }

    // 调用父组件传入的保存回调
    widget.onSave?.call();
  }

  void _backToScan(WidgetRef ref) {
    ref.read(addRecordControllerProvider.notifier).setInputType(AddRecordInputType.scan);
  }
}
