import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:health_diary/types/health_record.dart';
import 'package:health_diary/types/health_types.dart';
import 'health_metric_widget.dart';

/// 今日健康概览卡片
class TodayHealthCard extends StatelessWidget {
  final Map<HealthRecordTypeEnum, TodayHealthOverview> overview;

  const TodayHealthCard({super.key, required this.overview});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('today_health'.tr(), style: Theme.of(context).textTheme.titleMedium),
              FaIcon(FontAwesomeIcons.heartPulse, color: Theme.of(context).colorScheme.onSurfaceVariant, size: 20),
            ],
          ),
          const SizedBox(height: 24),

          // 动态构建健康指标
          _buildHealthMetrics(context),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 动态构建健康指标
  Widget _buildHealthMetrics(BuildContext context) {
    final metrics = <Widget>[];

    // 遍历所有健康记录类型
    for (final entry in overview.entries) {
      final type = entry.key;
      final healthOverview = entry.value;

      final overview = HealthMetricWidget(
        label: type.name.tr(),
        value: healthOverview.value ?? '--',
        unit: healthOverview.unit,
        icon: FaIcon(
          healthOverview.icon,
          size: 16,
          color: healthOverview.iconColor,
        ),
        isAbnormal: healthOverview.isAbnormal,
      );
      metrics.add(overview);
    }

    // 使用 Row 来平均分布
    return Row(
      children: _buildRowChildren(metrics),
    );
  }

  /// 构建 Row 的子组件，处理间距
  List<Widget> _buildRowChildren(List<Widget> metrics) {
    if (metrics.isEmpty) return [];
    if (metrics.length == 1) return [Expanded(child: metrics.first)];

    final children = <Widget>[];
    for (int i = 0; i < metrics.length; i++) {
      children.add(Expanded(child: metrics[i]));
      if (i < metrics.length - 1) {
        children.add(const SizedBox(width: 16));
      }
    }
    return children;
  }

  /// 构建查看详细报告按钮
  Widget _buildViewDetailedReportButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          // TODO: 实现查看详细报告功能
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'view_detailed_report'.tr(),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
